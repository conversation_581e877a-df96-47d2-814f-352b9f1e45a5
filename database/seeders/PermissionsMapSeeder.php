<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class PermissionsMapSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $trainer = Role::whereName('Trainer')->first();
        $moderator = Role::whereName('Content Moderator')->first();
        $super = Role::whereName('Super Admin')->first();
        $learner = Role::whereName('Learner')->first();

        // Trainers: manage courses/quizzes they create
        $trainer->givePermissionTo([
            'view_course','create_course','update_course',
            'view_module','create_module','update_module',
            'view_lesson','create_lesson','update_lesson',
            'view_quiz','create_quiz','update_quiz',
            // etc.
        ]);

        // Moderators: approve/publish
        $moderator->givePermissionTo([
            'approve_course','publish_course','archive_course',
            // etc.
        ]);

        // Super Admin: everything
        $super->givePermissionTo(Permission::pluck('name')->all());

        // Learners: read-only where appropriate
        $learner->givePermissionTo(['view_published_course']);
    }
}
