<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class PermissionsMapSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // First, create the permissions if they don't exist
        $permissions = [
            'view_course', 'create_course', 'update_course', 'delete_course',
            'view_module', 'create_module', 'update_module', 'delete_module',
            'view_lesson', 'create_lesson', 'update_lesson', 'delete_lesson',
            'view_quiz', 'create_quiz', 'update_quiz', 'delete_quiz',
            'approve_course', 'publish_course', 'archive_course',
            'view_published_course',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Create roles if they don't exist
        $trainer = Role::firstOrCreate(['name' => 'Trainer']);
        $moderator = Role::firstOrCreate(['name' => 'Content Moderator']);
        $super = Role::firstOrCreate(['name' => 'Super Admin']);
        $learner = Role::firstOrCreate(['name' => 'Learner']);

        // Trainers: manage courses/quizzes they create
        $trainer->givePermissionTo([
            'view_course','create_course','update_course',
            'view_module','create_module','update_module',
            'view_lesson','create_lesson','update_lesson',
            'view_quiz','create_quiz','update_quiz',
        ]);

        // Moderators: approve/publish
        $moderator->givePermissionTo([
            'approve_course','publish_course','archive_course',
        ]);

        // Super Admin: everything
        $super->givePermissionTo(Permission::pluck('name')->all());

        // Learners: read-only where appropriate
        $learner->givePermissionTo(['view_published_course']);
    }
}
