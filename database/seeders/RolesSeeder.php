<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;

class RolesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        foreach (['Learner','Trainer','Content Moderator','Super Admin'] as $r) {
            Role::firstOrCreate(['name'=>$r,'guard_name'=>'web']);
        }
    }
}
