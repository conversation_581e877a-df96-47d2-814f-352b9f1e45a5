<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pathway_courses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('pathway_id')->constrained('course_pathways');
            $table->foreignId('course_id')->constrained('courses');
            $table->unsignedInteger('sequence_order')->default(1);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pathway_courses');
    }
};
