/* Custom red theme styles for Filament */

/* Top navigation bar styling */
.fi-topbar {
    background-color: #dc2626 !important;
    border-color: #b91c1c !important;
}

.fi-topbar nav {
    background-color: #dc2626 !important;
}

/* Logo styling - make it white */
.fi-logo {
    color: white !important;
}

.fi-logo svg {
    color: white !important;
    fill: white !important;
}

/* Navigation items in topbar */
.fi-topbar .fi-topbar-item {
    color: white !important;
}

.fi-topbar .fi-topbar-item:hover {
    color: #fecaca !important;
}

/* User menu button */
.fi-topbar .fi-dropdown-trigger {
    color: white !important;
}

.fi-topbar .fi-dropdown-trigger:hover {
    color: #fecaca !important;
}

/* Sidebar brand area */
.fi-sidebar-header {
    background-color: #dc2626 !important;
}

.fi-sidebar-header .fi-logo {
    color: white !important;
}

/* Navigation links */
.fi-sidebar-nav-item-active {
    background-color: #fee2e2 !important;
    color: #7f1d1d !important;
}

.fi-sidebar-nav-item:hover {
    background-color: #fef2f2 !important;
}

/* Primary buttons */
.fi-btn-primary {
    background-color: #dc2626 !important;
}

.fi-btn-primary:hover {
    background-color: #b91c1c !important;
}

/* Badges */
.fi-badge-color-primary {
    background-color: #fee2e2 !important;
    color: #991b1b !important;
}

/* Additional topbar styling */
.fi-topbar-start .fi-logo,
.fi-topbar-start .fi-logo svg,
.fi-topbar-start .fi-logo path {
    color: white !important;
    fill: white !important;
}

/* Sidebar logo */
.fi-sidebar-header .fi-logo,
.fi-sidebar-header .fi-logo svg,
.fi-sidebar-header .fi-logo path {
    color: white !important;
    fill: white !important;
}

/* Global search */
.fi-topbar .fi-global-search-field {
    background-color: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(255, 255, 255, 0.2) !important;
    color: white !important;
}

.fi-topbar .fi-global-search-field::placeholder {
    color: rgba(255, 255, 255, 0.7) !important;
}

/* User avatar and name */
.fi-topbar .fi-avatar {
    border-color: rgba(255, 255, 255, 0.2) !important;
}

.fi-topbar .fi-dropdown-trigger span {
    color: white !important;
}
