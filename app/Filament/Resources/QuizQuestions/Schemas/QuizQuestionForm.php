<?php

namespace App\Filament\Resources\QuizQuestions\Schemas;

use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Schemas\Schema;

class QuizQuestionForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('quiz_id')
                    ->required()
                    ->numeric(),
                Textarea::make('question_text')
                    ->required()
                    ->columnSpanFull(),
                TextInput::make('question_type')
                    ->required(),
                Textarea::make('options_json')
                    ->columnSpanFull(),
                TextInput::make('correct_answer'),
            ]);
    }
}
