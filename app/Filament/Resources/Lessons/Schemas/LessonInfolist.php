<?php

namespace App\Filament\Resources\Lessons\Schemas;

use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Schema;

class LessonInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextEntry::make('module_id')
                    ->numeric(),
                TextEntry::make('title'),
                TextEntry::make('content_type'),
                TextEntry::make('file_url'),
                TextEntry::make('sequence_order')
                    ->numeric(),
                TextEntry::make('drip_release_days')
                    ->numeric(),
                TextEntry::make('duration_minutes')
                    ->numeric(),
                TextEntry::make('created_at')
                    ->dateTime(),
                TextEntry::make('updated_at')
                    ->dateTime(),
            ]);
    }
}
