<?php

namespace App\Filament\Resources\Lessons\Schemas;

use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class LessonForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('module_id')
                    ->required()
                    ->numeric(),
                TextInput::make('title')
                    ->required(),
                TextInput::make('content_type')
                    ->required(),
                TextInput::make('file_url'),
                TextInput::make('sequence_order')
                    ->required()
                    ->numeric()
                    ->default(1),
                TextInput::make('drip_release_days')
                    ->required()
                    ->numeric()
                    ->default(0),
                TextInput::make('duration_minutes')
                    ->numeric(),
            ]);
    }
}
