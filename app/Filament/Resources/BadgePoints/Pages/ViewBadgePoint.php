<?php

namespace App\Filament\Resources\BadgePoints\Pages;

use App\Filament\Resources\BadgePoints\BadgePointResource;
use Filament\Actions\EditAction;
use Filament\Resources\Pages\ViewRecord;

class ViewBadgePoint extends ViewRecord
{
    protected static string $resource = BadgePointResource::class;

    protected function getHeaderActions(): array
    {
        return [
            EditAction::make(),
        ];
    }
}
