<?php

namespace App\Filament\Resources\BadgePoints\Pages;

use App\Filament\Resources\BadgePoints\BadgePointResource;
use Filament\Actions\DeleteAction;
use Filament\Actions\ViewAction;
use Filament\Resources\Pages\EditRecord;

class EditBadgePoint extends EditRecord
{
    protected static string $resource = BadgePointResource::class;

    protected function getHeaderActions(): array
    {
        return [
            ViewAction::make(),
            DeleteAction::make(),
        ];
    }
}
