<?php

namespace App\Filament\Resources\BadgePoints\Pages;

use App\Filament\Resources\BadgePoints\BadgePointResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;

class ListBadgePoints extends ListRecords
{
    protected static string $resource = BadgePointResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make(),
        ];
    }
}
