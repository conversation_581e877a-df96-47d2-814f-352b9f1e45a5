<?php

namespace App\Filament\Resources\BadgePoints;

use App\Filament\Resources\BadgePoints\Pages\CreateBadgePoint;
use App\Filament\Resources\BadgePoints\Pages\EditBadgePoint;
use App\Filament\Resources\BadgePoints\Pages\ListBadgePoints;
use App\Filament\Resources\BadgePoints\Pages\ViewBadgePoint;
use App\Filament\Resources\BadgePoints\Schemas\BadgePointForm;
use App\Filament\Resources\BadgePoints\Schemas\BadgePointInfolist;
use App\Filament\Resources\BadgePoints\Tables\BadgePointsTable;
use App\Models\BadgePoint;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;

class BadgePointResource extends Resource
{
    protected static ?string $model = BadgePoint::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedRectangleStack;

    protected static ?string $recordTitleAttribute = 'Badge Point';

    public static function form(Schema $schema): Schema
    {
        return BadgePointForm::configure($schema);
    }

    public static function infolist(Schema $schema): Schema
    {
        return BadgePointInfolist::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return BadgePointsTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListBadgePoints::route('/'),
            'create' => CreateBadgePoint::route('/create'),
            'view' => ViewBadgePoint::route('/{record}'),
            'edit' => EditBadgePoint::route('/{record}/edit'),
        ];
    }
}
