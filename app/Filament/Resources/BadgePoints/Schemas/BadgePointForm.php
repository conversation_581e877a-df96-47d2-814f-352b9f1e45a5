<?php

namespace App\Filament\Resources\BadgePoints\Schemas;

use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class BadgePointForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('user_id')
                    ->required()
                    ->numeric(),
                TextInput::make('badge_name')
                    ->required(),
                TextInput::make('points_awarded')
                    ->required()
                    ->numeric()
                    ->default(0),
                DateTimePicker::make('awarded_at'),
            ]);
    }
}
