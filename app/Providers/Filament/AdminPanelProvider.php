<?php

namespace App\Providers\Filament;

use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\AuthenticateSession;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Pages\Dashboard;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\Widgets\AccountWidget;
use Filament\Widgets\FilamentInfoWidget;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;

class AdminPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->default()
            ->id('admin')
            ->path('admin')
            ->login()
            ->colors([
                'primary' => Color::Red,
            ])
            ->brandName('Physio LMS')
            ->favicon(asset('favicon.ico'))
            ->renderHook(
                'panels::head.end',
                fn (): string => '<style>
                    /* Top navigation bar styling - Filament v4 */
                    .fi-topbar,
                    [data-slot="topbar"],
                    .fi-header {
                        background-color: #dc2626 !important;
                        border-color: #b91c1c !important;
                    }

                    /* Logo styling - make it white */
                    .fi-logo,
                    .fi-logo svg,
                    .fi-logo path,
                    [data-slot="logo"],
                    [data-slot="logo"] svg,
                    [data-slot="logo"] path {
                        color: white !important;
                        fill: white !important;
                    }

                    /* Navigation items in topbar */
                    .fi-topbar .fi-topbar-item,
                    .fi-topbar button,
                    .fi-topbar a,
                    [data-slot="topbar"] button,
                    [data-slot="topbar"] a {
                        color: white !important;
                    }

                    .fi-topbar .fi-topbar-item:hover,
                    .fi-topbar button:hover,
                    .fi-topbar a:hover,
                    [data-slot="topbar"] button:hover,
                    [data-slot="topbar"] a:hover {
                        color: #fecaca !important;
                    }

                    /* User menu and dropdown */
                    .fi-topbar .fi-dropdown-trigger,
                    [data-slot="topbar"] [data-slot="trigger"] {
                        color: white !important;
                    }

                    /* Sidebar brand area */
                    .fi-sidebar-header,
                    [data-slot="sidebar-header"] {
                        background-color: #dc2626 !important;
                    }

                    /* Global search */
                    .fi-topbar input,
                    [data-slot="topbar"] input {
                        background-color: rgba(255, 255, 255, 0.1) !important;
                        border-color: rgba(255, 255, 255, 0.2) !important;
                        color: white !important;
                    }

                    .fi-topbar input::placeholder,
                    [data-slot="topbar"] input::placeholder {
                        color: rgba(255, 255, 255, 0.7) !important;
                    }

                    /* User avatar and text */
                    .fi-topbar .fi-avatar,
                    [data-slot="topbar"] [data-slot="avatar"] {
                        border-color: rgba(255, 255, 255, 0.2) !important;
                    }

                    /* Any text in topbar */
                    .fi-topbar span,
                    .fi-topbar p,
                    [data-slot="topbar"] span,
                    [data-slot="topbar"] p {
                        color: white !important;
                    }
                </style>'
            )
            ->discoverResources(in: app_path('Filament/Resources'), for: 'App\Filament\Resources')
            ->discoverPages(in: app_path('Filament/Pages'), for: 'App\Filament\Pages')
            ->pages([
                Dashboard::class,
            ])
            ->discoverWidgets(in: app_path('Filament/Widgets'), for: 'App\Filament\Widgets')
            ->widgets([
                AccountWidget::class,
                FilamentInfoWidget::class,
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
            ]);
    }
}
